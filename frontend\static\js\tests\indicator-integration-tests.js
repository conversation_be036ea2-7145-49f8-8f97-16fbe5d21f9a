/**
 * Indicator Integration Tests
 * Tests for the enhanced indicator functionality
 */

class IndicatorIntegrationTests {
    constructor() {
        this.testResults = [];
    }

    async runAllTests() {
        console.log('🧪 Starting Indicator Integration Tests...');
        
        await this.testIndicatorConfigManagerInitialization();
        await this.testAdvancedIndicatorPlotterInitialization();
        await this.testEventHandling();
        await this.testPlotButtonState();
        
        this.displayResults();
    }

    async testIndicatorConfigManagerInitialization() {
        try {
            const hasManager = !!window.multiIndicatorConfig;
            const hasLoadMethod = hasManager && typeof window.multiIndicatorConfig.loadStrategyIndicators === 'function';
            const hasPlotMethod = hasManager && typeof window.multiIndicatorConfig.plotIndicators === 'function';
            
            this.addResult('Multi-Indicator Config Manager Initialization', 
                hasManager && hasLoadMethod && hasPlotMethod,
                `Manager exists: ${hasManager}, Load method: ${hasLoadMethod}, Plot method: ${hasPlotMethod}`
            );
        } catch (error) {
            this.addResult('Multi-Indicator Config Manager Initialization', false, error.message);
        }
    }

    async testAdvancedIndicatorPlotterInitialization() {
        try {
            const hasPlotter = !!window.advancedIndicatorPlotter;
            const hasHandleMethod = hasPlotter && typeof window.advancedIndicatorPlotter.handleIndicatorChange === 'function';
            
            this.addResult('Advanced Indicator Plotter Initialization', 
                hasPlotter && hasHandleMethod,
                `Plotter exists: ${hasPlotter}, Handle method: ${hasHandleMethod}`
            );
        } catch (error) {
            this.addResult('Advanced Indicator Plotter Initialization', false, error.message);
        }
    }

    async testEventHandling() {
        try {
            // Test if events can be dispatched without errors
            let eventReceived = false;
            
            const testListener = () => { eventReceived = true; };
            document.addEventListener('indicatorsChanged', testListener);
            
            document.dispatchEvent(new CustomEvent('indicatorsChanged', {
                detail: { strategyId: 1, indicators: {} }
            }));
            
            // Small delay to allow event processing
            await new Promise(resolve => setTimeout(resolve, 100));
            
            document.removeEventListener('indicatorsChanged', testListener);
            
            this.addResult('Event Handling', eventReceived, 
                `Event received: ${eventReceived}`);
        } catch (error) {
            this.addResult('Event Handling', false, error.message);
        }
    }

    async testPlotButtonState() {
        try {
            const plotBtn = document.getElementById('plot-indicators-btn');
            const hasButton = !!plotBtn;
            const hasDisabledState = hasButton && plotBtn.hasAttribute('disabled');
            
            this.addResult('Plot Button State Management', hasButton,
                `Button exists: ${hasButton}, Has disabled state: ${hasDisabledState}`
            );
        } catch (error) {
            this.addResult('Plot Button State Management', false, error.message);
        }
    }

    addResult(testName, passed, details) {
        this.testResults.push({
            name: testName,
            passed: passed,
            details: details,
            timestamp: new Date().toISOString()
        });
    }

    displayResults() {
        console.log('\n📊 Indicator Integration Test Results:');
        console.log('=' .repeat(50));
        
        let passedCount = 0;
        this.testResults.forEach(result => {
            const status = result.passed ? '✅ PASS' : '❌ FAIL';
            console.log(`${status} ${result.name}`);
            if (result.details) {
                console.log(`   Details: ${result.details}`);
            }
            if (result.passed) passedCount++;
        });
        
        console.log('=' .repeat(50));
        console.log(`Results: ${passedCount}/${this.testResults.length} tests passed`);
        
        if (passedCount === this.testResults.length) {
            console.log('🎉 All tests passed!');
        } else {
            console.log('⚠️  Some tests failed. Check implementation.');
        }
    }
}

// Global test runner
window.runIndicatorTests = async function() {
    const tester = new IndicatorIntegrationTests();
    await tester.runAllTests();
};

// Auto-run tests when DOM is loaded (if in development mode)
document.addEventListener('DOMContentLoaded', () => {
    // Only run tests if explicitly requested or in development
    if (window.location.search.includes('test=indicators') || 
        window.location.hostname === 'localhost') {
        setTimeout(() => {
            console.log('🔧 Development mode detected. Run window.runIndicatorTests() to test indicator functionality.');
        }, 2000);
    }
});

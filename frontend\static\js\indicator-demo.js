/**
 * Indicator Feature Demo
 * Demonstrates the enhanced indicator functionality
 */

class IndicatorDemo {
    constructor() {
        this.demoSteps = [];
        this.currentStep = 0;
        this.isRunning = false;
    }

    async runDemo() {
        if (this.isRunning) {
            console.log('Demo is already running');
            return;
        }

        this.isRunning = true;
        console.log('🎬 Starting Indicator Feature Demo...');
        
        this.setupDemoSteps();
        
        for (let i = 0; i < this.demoSteps.length; i++) {
            this.currentStep = i;
            const step = this.demoSteps[i];
            
            console.log(`\n📍 Step ${i + 1}: ${step.name}`);
            console.log(`   ${step.description}`);
            
            try {
                await step.action();
                console.log(`   ✅ Completed: ${step.name}`);
                
                // Wait between steps for visibility
                if (i < this.demoSteps.length - 1) {
                    await this.wait(2000);
                }
            } catch (error) {
                console.error(`   ❌ Failed: ${step.name}`, error);
                break;
            }
        }
        
        this.isRunning = false;
        console.log('\n🎉 Demo completed!');
    }

    setupDemoSteps() {
        this.demoSteps = [
            {
                name: 'Check Prerequisites',
                description: 'Verify all required components are loaded',
                action: async () => {
                    this.checkComponent('multiIndicatorConfig', 'Multi-Indicator Config Manager');
                    this.checkComponent('advancedIndicatorPlotter', 'Advanced Indicator Plotter');
                    this.checkComponent('professionalChart', 'Professional Chart');
                    this.checkElement('plot-indicators-btn', 'Plot Indicators Button');
                }
            },
            {
                name: 'Simulate Strategy Selection',
                description: 'Simulate selecting a strategy',
                action: async () => {
                    if (window.multiIndicatorConfig) {
                        // Simulate strategy selection
                        document.dispatchEvent(new CustomEvent('strategyChanged', {
                            detail: { strategyId: 1 }
                        }));
                        console.log('   📋 Strategy selection event dispatched');
                    }
                }
            },
            {
                name: 'Check Plot Button State',
                description: 'Verify plot button responds to state changes',
                action: async () => {
                    const plotBtn = document.getElementById('plot-indicators-btn');
                    if (plotBtn) {
                        const isDisabled = plotBtn.disabled;
                        const tooltip = plotBtn.title;
                        console.log(`   🔘 Button disabled: ${isDisabled}`);
                        console.log(`   💬 Button tooltip: "${tooltip}"`);
                    }
                }
            },
            {
                name: 'Simulate Chart Data Loading',
                description: 'Simulate chart data being loaded',
                action: async () => {
                    // Create mock chart data
                    const mockData = this.generateMockChartData();
                    
                    document.dispatchEvent(new CustomEvent('chartDataLoaded', {
                        detail: {
                            candleData: mockData,
                            ohlcvData: mockData,
                            symbol: 'BTCUSDT',
                            timeframe: '15m'
                        }
                    }));
                    console.log(`   📊 Chart data loaded event dispatched with ${mockData.length} candles`);
                }
            },
            {
                name: 'Test Indicator Addition',
                description: 'Simulate adding an indicator',
                action: async () => {
                    if (window.multiIndicatorConfig && window.multiIndicatorConfig.currentStrategy) {
                        // Simulate adding RSI indicator
                        const mockConfig = {
                            indicator_name: 'RSI',
                            config: { period: 14, color: '#FF6B6B' },
                            is_enabled: true,
                            display_order: 0,
                            display_name: 'RSI (14)',
                            chart_type: 'subchart'
                        };
                        
                        window.multiIndicatorConfig.currentConfigs['RSI'] = mockConfig;
                        window.multiIndicatorConfig.updatePlotButtonState();
                        
                        console.log('   📈 RSI indicator added to configuration');
                    }
                }
            },
            {
                name: 'Test Event System',
                description: 'Test the indicator event system',
                action: async () => {
                    // Test indicatorsChanged event
                    document.dispatchEvent(new CustomEvent('indicatorsChanged', {
                        detail: {
                            strategyId: 1,
                            indicators: {
                                RSI: { period: 14, color: '#FF6B6B' }
                            }
                        }
                    }));
                    console.log('   🔄 indicatorsChanged event dispatched');
                }
            },
            {
                name: 'Verify Final State',
                description: 'Check final state of all components',
                action: async () => {
                    const plotBtn = document.getElementById('plot-indicators-btn');
                    const hasStrategy = !!(window.multiIndicatorConfig?.currentStrategy);
                    const hasConfigs = Object.keys(window.multiIndicatorConfig?.currentConfigs || {}).length > 0;
                    
                    console.log('   📊 Final State Summary:');
                    console.log(`      - Strategy selected: ${hasStrategy}`);
                    console.log(`      - Indicators configured: ${hasConfigs}`);
                    console.log(`      - Plot button enabled: ${plotBtn ? !plotBtn.disabled : false}`);
                }
            }
        ];
    }

    checkComponent(windowProperty, name) {
        const exists = !!window[windowProperty];
        if (exists) {
            console.log(`   ✅ ${name} is loaded`);
        } else {
            throw new Error(`${name} is not loaded`);
        }
    }

    checkElement(elementId, name) {
        const element = document.getElementById(elementId);
        if (element) {
            console.log(`   ✅ ${name} element found`);
        } else {
            throw new Error(`${name} element not found`);
        }
    }

    generateMockChartData() {
        const data = [];
        const baseTime = Math.floor(Date.now() / 1000) - (100 * 15 * 60); // 100 candles back
        let price = 50000;
        
        for (let i = 0; i < 100; i++) {
            const time = baseTime + (i * 15 * 60); // 15-minute intervals
            const change = (Math.random() - 0.5) * 1000;
            const open = price;
            const close = price + change;
            const high = Math.max(open, close) + Math.random() * 200;
            const low = Math.min(open, close) - Math.random() * 200;
            
            data.push({
                time: time,
                open: open,
                high: high,
                low: low,
                close: close,
                volume: Math.random() * 1000000
            });
            
            price = close;
        }
        
        return data;
    }

    wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async quickDemo() {
        console.log('🚀 Running Quick Indicator Demo...');
        
        // Quick test of key functionality
        try {
            // Test component availability
            this.checkComponent('multiIndicatorConfig', 'Multi-Indicator Config Manager');
            
            // Test event dispatch
            document.dispatchEvent(new CustomEvent('indicatorsChanged', {
                detail: { strategyId: 1, indicators: {} }
            }));
            
            console.log('✅ Quick demo completed successfully');
        } catch (error) {
            console.error('❌ Quick demo failed:', error);
        }
    }
}

// Global demo functions
window.runIndicatorDemo = async function() {
    const demo = new IndicatorDemo();
    await demo.runDemo();
};

window.runQuickIndicatorDemo = async function() {
    const demo = new IndicatorDemo();
    await demo.quickDemo();
};

// Auto-setup demo commands
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        console.log('🎬 Indicator Demo Available:');
        console.log('   - Run window.runIndicatorDemo() for full demo');
        console.log('   - Run window.runQuickIndicatorDemo() for quick test');
    }, 3000);
});

/* Multi-Indicator Configuration Styles */

.indicator-config-panel {
    background: #1e1e1e;
    border: 1px solid #333;
    border-radius: 8px;
    padding: 20px;
    margin: 10px 0;
    color: #d1d4dc;
}

.indicator-config-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #333;
}

.indicator-config-title {
    font-size: 18px;
    font-weight: bold;
    color: #ffffff;
}

.indicator-config-controls {
    display: flex;
    gap: 10px;
}

.btn-save-config,
.btn-reset-config,
.btn-plot-indicators {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.btn-save-config {
    background: #4CAF50;
    color: white;
}

.btn-save-config:hover {
    background: #45a049;
}

.btn-save-config.modified {
    background: #FF9800;
    animation: pulse 2s infinite;
}

.btn-reset-config {
    background: #f44336;
    color: white;
}

.btn-reset-config:hover {
    background: #da190b;
}

.btn-plot-indicators {
    background: #2196F3;
    color: white;
}

.btn-plot-indicators:hover {
    background: #1976D2;
}

.btn-plot-indicators:disabled {
    background: #cccccc;
    cursor: not-allowed;
}

/* Strategy Status Section */
.strategy-status {
    margin-bottom: 20px;
    padding: 12px;
    border-radius: 6px;
    background: #2a2a2a;
    border: 1px solid #444;
}

.status-message {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.status-message.success {
    color: #4CAF50;
    background: rgba(76, 175, 80, 0.1);
    border-color: #4CAF50;
}

.status-message.warning {
    color: #FF9800;
    background: rgba(255, 152, 0, 0.1);
    border-color: #FF9800;
}

.status-icon {
    font-size: 16px;
}

.status-text {
    font-weight: 500;
}

/* Add Indicator Section */
.add-indicator-section {
    margin-bottom: 20px;
    padding: 16px;
    background: #1e1e1e;
    border: 1px solid #333;
    border-radius: 6px;
}

.add-indicator-section h4 {
    margin: 0 0 12px 0;
    color: #4CAF50;
    font-size: 16px;
}

.add-indicator-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.indicator-select {
    flex: 1;
    padding: 8px 12px;
    background: #2a2a2a;
    border: 1px solid #444;
    border-radius: 4px;
    color: #d1d4dc;
    font-size: 14px;
}

.indicator-select:focus {
    outline: none;
    border-color: #4CAF50;
}

.btn-add-indicator {
    padding: 8px 16px;
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.btn-add-indicator:hover:not(:disabled) {
    background: #45a049;
}

.btn-add-indicator:disabled {
    background: #666;
    cursor: not-allowed;
    opacity: 0.6;
}

/* Message System */
.indicator-messages {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    max-width: 400px;
}

.indicator-message {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 16px;
    margin-bottom: 10px;
    border-radius: 6px;
    font-size: 14px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.indicator-message.success {
    background: #4CAF50;
    color: white;
    border-left: 4px solid #45a049;
}

.indicator-message.error {
    background: #f44336;
    color: white;
    border-left: 4px solid #da190b;
}

.indicator-message.info {
    background: #2196F3;
    color: white;
    border-left: 4px solid #1976D2;
}

.message-icon {
    font-size: 16px;
}

.message-text {
    flex: 1;
}

.message-close {
    background: none;
    border: none;
    color: inherit;
    font-size: 18px;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.message-close:hover {
    background: rgba(255,255,255,0.2);
}

/* No Strategy/Indicators Messages */
.no-strategy-message,
.no-indicators-message {
    text-align: center;
    padding: 40px 20px;
    color: #888;
    font-style: italic;
    background: #1a1a1a;
    border: 2px dashed #333;
    border-radius: 8px;
    margin: 20px 0;
}

.no-indicators-message {
    background: #1e1e1e;
    border-color: #444;
    color: #aaa;
}

/* Enhanced Indicator Panel */
.indicator-panel {
    position: relative;
}

.indicator-panel .remove-indicator {
    position: absolute;
    top: 8px;
    right: 8px;
    background: #f44336;
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.indicator-panel:hover .remove-indicator {
    opacity: 1;
}

.indicator-panel .remove-indicator:hover {
    background: #d32f2f;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Indicator Panel Styles */
#indicator-config-container {
    max-height: 600px;
    overflow-y: auto;
    padding: 10px;
}

.indicator-panel {
    background: #2a2a2a;
    border: 1px solid #444;
    border-radius: 6px;
    margin-bottom: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.indicator-panel:hover {
    border-color: #666;
}

.indicator-panel.expanded {
    border-color: #4CAF50;
}

.indicator-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #333;
    cursor: pointer;
    user-select: none;
    transition: background 0.3s ease;
}

.indicator-header:hover {
    background: #3a3a3a;
}

.indicator-title {
    display: flex;
    align-items: center;
    gap: 10px;
}

.indicator-title input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.indicator-name {
    font-weight: bold;
    color: #ffffff;
    margin-right: 10px;
}

.indicator-type {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.indicator-type.overlay {
    background: #4CAF50;
    color: white;
}

.indicator-type.subchart {
    background: #2196F3;
    color: white;
}

.indicator-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.btn-reset {
    padding: 4px 8px;
    background: #666;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    transition: background 0.3s ease;
}

.btn-reset:hover {
    background: #777;
}

.expand-icon {
    font-size: 12px;
    transition: transform 0.3s ease;
}

.indicator-panel.expanded .expand-icon {
    transform: rotate(180deg);
}

/* Indicator Content Styles */
.indicator-content {
    display: none;
    padding: 16px;
    background: #2a2a2a;
    border-top: 1px solid #444;
}

.indicator-panel.expanded .indicator-content {
    display: block;
}

.config-section {
    margin-bottom: 20px;
}

.config-section h4 {
    color: #ffffff;
    margin-bottom: 12px;
    font-size: 14px;
    font-weight: bold;
    border-bottom: 1px solid #444;
    padding-bottom: 4px;
}

.config-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    padding: 8px 0;
}

.config-row label {
    color: #d1d4dc;
    font-size: 14px;
    min-width: 120px;
}

.config-row input[type="number"],
.config-row input[type="range"] {
    background: #1e1e1e;
    border: 1px solid #555;
    border-radius: 4px;
    color: #d1d4dc;
    padding: 6px 8px;
    font-size: 14px;
    width: 80px;
}

.config-row input[type="number"]:focus,
.config-row input[type="range"]:focus {
    outline: none;
    border-color: #4CAF50;
}

.config-row input[type="color"] {
    width: 40px;
    height: 30px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    background: none;
}

.config-row span {
    color: #888;
    font-size: 12px;
    min-width: 40px;
    text-align: right;
}

/* Multi-Period Configuration */
.multi-period-config {
    background: #1e1e1e;
    border: 1px solid #444;
    border-radius: 4px;
    padding: 12px;
    margin-bottom: 12px;
}

.period-row {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
    padding: 6px;
    background: #333;
    border-radius: 4px;
}

.period-input {
    width: 80px;
    background: #1e1e1e;
    border: 1px solid #555;
    border-radius: 3px;
    color: #d1d4dc;
    padding: 4px 6px;
    font-size: 13px;
}

.color-input {
    width: 35px;
    height: 25px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
}

.btn-remove {
    width: 25px;
    height: 25px;
    background: #f44336;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-remove:hover {
    background: #da190b;
}

.btn-add-period {
    width: 100%;
    padding: 8px;
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    margin-top: 8px;
    transition: background 0.3s ease;
}

.btn-add-period:hover {
    background: #45a049;
}

/* Message Styles */
.config-message {
    padding: 12px 16px;
    border-radius: 4px;
    margin-bottom: 16px;
    font-size: 14px;
    display: none;
}

.config-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.config-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.config-message.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Scrollbar Styles */
#indicator-config-container::-webkit-scrollbar {
    width: 8px;
}

#indicator-config-container::-webkit-scrollbar-track {
    background: #1e1e1e;
    border-radius: 4px;
}

#indicator-config-container::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 4px;
}

#indicator-config-container::-webkit-scrollbar-thumb:hover {
    background: #666;
}

/* Responsive Design */
@media (max-width: 768px) {
    .indicator-config-panel {
        padding: 15px;
    }
    
    .config-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .config-row label {
        min-width: auto;
    }
    
    .period-row {
        flex-wrap: wrap;
    }
    
    .indicator-header {
        padding: 10px 12px;
    }
    
    .indicator-title {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}
